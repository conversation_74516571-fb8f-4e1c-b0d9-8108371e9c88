# Novel Translation Application - Status & Documentation

## Overview

The Novel Translation Application is a comprehensive web-based system for crawling, translating, and managing Japanese and Chinese web novels. The application provides AI-powered translation with advanced features for maintaining consistency, formatting, and quality across large-scale novel projects.

**Version:** 1.0.0  
**Primary Language:** PHP 8.2  
**Database:** MySQL with UTF8MB4 support  
**Frontend:** Bootstrap 5 + JavaScript  

## Core Capabilities

### 1. Multi-Platform Novel Crawling
- **Kakuyomu** (kakuyomu.jp) - Japanese novels
- **Syosetu** (ncode.syosetu.com) - Japanese novels
- **69书吧** (69shuba.cx) - Chinese novels
- **大熊猫文学** (dxmwx.org) - Chinese novels
- **Manual Entry** - For unsupported platforms or custom content

### 2. AI Translation Services
- **Primary:** DeepSeek AI (deepseek-chat model)
- **Fallback:** Google Gemini (1.5-flash and 2.0-flash models)
- **Automatic failover** between services for reliability
- **Cache optimization** for cost reduction and performance

### 3. Advanced Translation Features
- **Dialogue Tag Prevention** - Ensures faithful translation without added attribution
- **Formatting Preservation** - Maintains original text structure and line breaks
- **Name Dictionary** - Consistent character/location name translations
- **Honorific Preservation** - Maintains Japanese honorifics in translation
- **Sound Effects Handling** - Translates onomatopoeia to English descriptions
- **Furigana Processing** - Handles Japanese reading aids

### 4. Content Management
- **User-Controlled Chapter Splitting** - Split chapters into 2-10 parts with intelligent boundary detection
- **Intelligent Chunking** - Automatic splitting of large chapters (5K-15K characters)
- **Chapter Editor** - Edit original and translated content
- **Translation Status Tracking** - Monitor progress across novels
- **Batch Operations** - Process multiple chapters efficiently

### 5. Export & Integration
- **Word Document Export** - Professional formatting for offline reading
- **WordPress Integration** - Direct publishing to WordPress sites
- **Excel Import/Export** - Bulk data management
- **Multi-domain WordPress** - Support for multiple WordPress installations

## System Architecture

### Core Services
```
TranslationService (Primary Controller)
├── DeepSeekTranslationService (Primary AI)
├── GeminiTranslationService (Fallback AI)
├── ChapterChunker (Content Splitting)
├── ChapterSplitter (User-Controlled Splitting)
├── HonorificService (Japanese Honorifics)
├── SoundEffectsService (Onomatopoeia)
├── FuriganaService (Reading Aids)
└── CacheOptimizationService (Performance)
```

### Data Layer
```
Database (MySQL)
├── novels (Novel metadata)
├── chapters (Chapter content)
├── chapter_chunks (Split content)
├── name_dictionary (Translation consistency)
├── translation_logs (API usage tracking)
├── wordpress_posts (Publishing status)
└── user_preferences (System settings)
```

### Web Crawlers
```
BaseCrawler (Abstract)
├── KakuyomuCrawler (Japanese)
├── SyosetuCrawler (Japanese)
├── Shuba69Crawler (Chinese)
└── DxmwxCrawler (Chinese)
```

## Current Configuration

### Translation Settings
- **Chunk Size:** 5,000-15,000 characters
- **Timeout:** 300 seconds (5 minutes)
- **Context Overlap:** 300 characters
- **Smart Chunking:** Enabled
- **Dynamic Chunking:** Enabled

### API Configuration
- **DeepSeek Model:** deepseek-chat (V3-0324)
- **Gemini Primary:** gemini-1.5-flash
- **Gemini Fallback:** gemini-2.0-flash
- **Cache Optimization:** Enabled
- **Retry Logic:** 3 attempts with exponential backoff

### Performance Features
- **Adaptive Token Limits** - Prevents truncation
- **Cache Hit Optimization** - Reduces API costs
- **Timeout Recovery** - Automatic chunk splitting
- **Error Recovery** - Graceful fallback handling

## Key Recent Improvements

### Chapter Splitting Feature (2025-06-16)
- **User-Controlled Splitting** - Split chapters into 2-10 parts with intelligent boundary detection
- **Smart Boundary Detection** - Respects paragraph and sentence boundaries for natural splits
- **Automatic Size Recommendations** - Suggests optimal split count based on content length
- **Seamless Translation Integration** - Split chapters translate individually to avoid API timeouts
- **Visual Indicators** - Clear UI indicators for chapters with splits

### Translation Quality (Latest)
- **Dialogue Tag Prevention** - Eliminates artificial "I said," "she replied," additions
- **Line Break Preservation** - Maintains original text formatting perfectly
- **Structure Validation** - Ensures translation matches source layout
- **Enhanced Prompting** - Improved AI instructions for faithful translation

### Performance Optimizations
- **Cache Optimization Service** - Reduces API costs by up to 40%
- **Intelligent Chunking** - Prevents timeouts on large content
- **Adaptive Timeouts** - Dynamic adjustment based on content size
- **Truncation Recovery** - Automatic retry with smaller chunks

### Content Processing
- **Enhanced Name Detection** - Improved character name extraction
- **Furigana Integration** - Better handling of Japanese reading aids
- **Sound Effects Translation** - Converts Japanese onomatopoeia to English
- **Honorific Preservation** - Maintains cultural elements

## Current Limitations

### Technical Constraints
- **API Rate Limits** - Dependent on external AI service availability
- **Platform Dependencies** - Crawling subject to source website changes

### Feature Gaps
- **Real-time Translation** - Currently batch-based processing
- **Multi-language Output** - Currently English-only translation
- **Advanced Formatting** - Limited support for complex text formatting

## System Requirements

### Server Environment
- **PHP:** 8.2 or higher
- **MySQL:** 5.7 or higher (UTF8MB4 support required)
- **Extensions:** cURL, JSON, PDO, ZIP, DOM
- **Memory:** 512MB minimum (1GB recommended)
- **Storage:** 1GB minimum for application + database

### External Dependencies
- **Composer packages** for Word export and PDF generation
- **DeepSeek API access** (primary translation service)
- **Google Gemini API access** (fallback translation service)
- **WordPress REST API** (optional, for publishing integration)

## Usage Workflow

### Basic Novel Processing
1. **Import Novel** - Enter URL or manual entry
2. **Preview Content** - Review novel info and chapter list
3. **Save Novel** - Add to local database
4. **Process Chapters** - Save original content from source
5. **Translate Chapters** - AI-powered translation with consistency
6. **Review & Edit** - Manual review and corrections
7. **Export/Publish** - Word documents or WordPress publishing

### Advanced Features
- **Name Dictionary Management** - Maintain translation consistency
- **Batch Processing** - Handle multiple chapters simultaneously
- **WordPress Integration** - Direct publishing to multiple sites
- **Excel Operations** - Bulk import/export for large projects

## Monitoring & Logging

### Translation Tracking
- **API Usage Logs** - Token consumption and costs
- **Performance Metrics** - Translation speed and success rates
- **Cache Performance** - Hit rates and cost savings
- **Error Tracking** - Failed translations and recovery

### System Health
- **Database Performance** - Query optimization and indexing
- **Memory Usage** - Monitoring for large content processing
- **API Response Times** - Service availability tracking
- **WordPress Connectivity** - Publishing service status

## Security Features

### Data Protection
- **Input Sanitization** - All user inputs validated and cleaned
- **SQL Injection Prevention** - Parameterized queries throughout
- **XSS Protection** - Output encoding and CSP headers
- **Session Security** - Secure session configuration

### API Security
- **API Key Management** - Secure storage of service credentials
- **Rate Limiting** - Protection against abuse
- **Error Handling** - No sensitive data in error messages
- **HTTPS Enforcement** - Secure communication with external APIs

## Future Development Priorities

### Short-term Enhancements
- **Real-time Translation Preview** - Live translation as you type
- **Advanced Formatting Support** - Better handling of complex layouts
- **Multi-language Output** - Support for additional target languages
- **Enhanced WordPress Themes** - Better integration with popular themes

### Long-term Goals
- **Machine Learning Integration** - Custom models for novel-specific translation
- **Collaborative Features** - Multi-user editing and review workflows
- **Advanced Analytics** - Detailed translation quality metrics
- **Mobile Application** - Native mobile app for content management

---

**Last Updated:** June 2025
**System Status:** Fully Operational
**Translation Quality:** High (with recent dialogue and formatting improvements)
**Reliability:** Excellent (dual AI service redundancy)
**Latest Feature:** User-controlled chapter splitting for handling large content
