<?php
/**
 * Dxmwx (dxmwx.org) Crawler
 * Novel Translation Application
 */

class DxmwxCrawler extends BaseCrawler {
    
    private const BASE_URL = 'https://www.dxmwx.org';
    
    /**
     * Validate Dxmwx URL
     */
    protected function validateUrl(string $url): bool {
        return preg_match('/dxmwx\.org\/book\/\d+\.html/', $url) === 1;
    }
    
    /**
     * Get novel information from Dxmwx
     */
    public function getNovelInfo(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid Dxmwx URL: {$url}");
        }
        
        $this->log("Fetching novel info from: {$url}");
        
        try {
            $html = $this->makeRequest($url);
            $dom = $this->parseHtml($html);
            
            // Extract novel information
            $title = $this->extractTitle($dom);
            $author = $this->extractAuthor($dom);
            $synopsis = $this->extractSynopsis($dom);
            $publishDate = $this->extractPublishDate($dom);
            $totalChapters = $this->extractTotalChapters($dom);
            
            $this->log("Successfully extracted novel info: {$title}");
            
            return [
                'platform' => 'dxmwx',
                'url' => $url,
                'original_title' => $title,
                'author' => $author,
                'original_synopsis' => $synopsis,
                'publication_date' => $publishDate,
                'total_chapters' => $totalChapters,
                'language' => 'zh'
            ];
            
        } catch (Exception $e) {
            $this->log("Error fetching novel info: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Get chapter list from Dxmwx
     */
    public function getChapterList(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid Dxmwx URL: {$url}");
        }

        // Extract novel ID from URL
        $novelId = $this->extractNovelId($url);
        $chapterListUrl = self::BASE_URL . '/chapter/' . $novelId . '.html';
        
        $this->log("Fetching chapter list from: {$chapterListUrl}");

        try {
            $html = $this->makeRequest($chapterListUrl);
            $dom = $this->parseHtml($html);

            $chapters = [];
            $chapterNumber = 1;

            // Find all chapter links
            $chapterElements = $this->querySelectorAll($dom, 'a[href*="/read/"]');
            
            foreach ($chapterElements as $element) {
                $chapterUrl = $element->getAttribute('href');
                $chapterTitle = $this->cleanText($element->textContent);

                // Filter valid chapter links
                if ($this->isValidChapterLink($chapterUrl, $chapterTitle, $novelId)) {
                    $fullChapterUrl = $this->normalizeUrl($chapterUrl, self::BASE_URL);

                    $chapters[] = [
                        'chapter_number' => $chapterNumber,
                        'chapter_url' => $fullChapterUrl,
                        'original_title' => $chapterTitle
                    ];
                    
                    $chapterNumber++;
                }
            }

            $this->log("Found " . count($chapters) . " chapters");
            return $chapters;

        } catch (Exception $e) {
            $this->log("Error fetching chapter list: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Get chapter content from Dxmwx
     */
    public function getChapterContent(string $chapterUrl): array {
        $this->log("Fetching chapter content from: {$chapterUrl}");

        try {
            $html = $this->makeRequest($chapterUrl);
            $dom = $this->parseHtml($html);

            // Extract chapter title
            $title = $this->extractChapterTitle($dom);

            // Extract chapter content
            $contentElement = $this->findContentElement($dom);
            if (!$contentElement) {
                throw new Exception("Chapter content not found");
            }

            $content = $this->extractChapterText($contentElement);

            $this->log("Successfully extracted chapter content");

            return [
                'original_title' => $title,
                'original_content' => $content,
                'word_count' => mb_strlen($content)
            ];

        } catch (Exception $e) {
            $this->log("Error fetching chapter content: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Extract novel ID from URL
     */
    private function extractNovelId(string $url): string {
        if (preg_match('/\/book\/(\d+)\.html/', $url, $matches)) {
            return $matches[1];
        }
        throw new Exception("Could not extract novel ID from URL: {$url}");
    }
    
    /**
     * Extract novel title
     */
    private function extractTitle(DOMDocument $dom): string {
        // Try multiple selectors for title
        $titleSelectors = [
            'h1',
            '.book-title',
            '.novel-title'
        ];

        foreach ($titleSelectors as $selector) {
            $titleElement = $this->querySelector($dom, $selector);
            if ($titleElement) {
                $title = $this->cleanText($titleElement->textContent);
                if (!empty($title) && !$this->isNavigationText($title)) {
                    return $title;
                }
            }
        }

        // Fallback: extract from page title
        $titleElements = $this->querySelectorAll($dom, 'title');
        if ($titleElements->length > 0) {
            $pageTitle = $titleElements->item(0)->textContent;
            // Remove common suffixes
            $title = preg_replace('/\s*(简介|全本阅读|TXT|大熊猫文学).*$/', '', $pageTitle);
            return $this->cleanText($title);
        }

        return '';
    }
    
    /**
     * Extract author name
     */
    private function extractAuthor(DOMDocument $dom): string {
        // Look for author links or text
        $authorSelectors = [
            'a[href*="/list/"]',
            '.author',
            '.writer'
        ];

        foreach ($authorSelectors as $selector) {
            $authorElements = $this->querySelectorAll($dom, $selector);
            foreach ($authorElements as $element) {
                $authorText = $this->cleanText($element->textContent);
                // Filter out non-author links
                if (!empty($authorText) && !$this->isNavigationText($authorText) && 
                    !preg_match('/(完本|都市|生活|轻松|更新时间)/', $authorText)) {
                    $this->log("Author extracted: {$authorText}");
                    return $authorText;
                }
            }
        }

        $this->log("Author extraction failed - no matching elements found", 'warning');
        return '';
    }
    
    /**
     * Extract synopsis
     */
    private function extractSynopsis(DOMDocument $dom): string {
        // Look for synopsis in the page content
        $xpath = new DOMXPath($dom);

        // Find text nodes that contain substantial content, prioritizing indented text
        $textNodes = $xpath->query('//text()[string-length(normalize-space(.)) > 50]');

        $candidates = [];

        foreach ($textNodes as $textNode) {
            $text = $this->cleanText($textNode->textContent);

            // Skip navigation and metadata
            if ($this->isNavigationText($text) ||
                preg_match('/(更新时间|最新章节|章节列表|作者其他作品|精彩推荐|大熊猫文学|网站地图)/', $text)) {
                continue;
            }

            // Check if this looks like a synopsis
            if (strlen($text) > 100 && strlen($text) < 2000) {
                $score = 0;

                // Score based on synopsis-like characteristics
                if (preg_match('/(如果|会用|做什么|答案|不论|还是|通通|让每|都成为|不过|得先|再跟|好好|本书又名)/', $text)) {
                    $score += 100;
                }

                // Look for story description patterns
                if (preg_match('/(拥有|创造|虚拟|世界|游戏|改编|构筑|基石|歪路|龙头|老大|干几架)/', $text)) {
                    $score += 50;
                }

                // Check if it starts with typical synopsis patterns
                if (preg_match('/^\s*(如果你|林游的|不论是|让每款|不过在|再跟|本书又名)/', $text)) {
                    $score += 75;
                }

                // Bonus for containing multiple sentences
                $score += substr_count($text, '。') * 10;
                $score += substr_count($text, '！') * 10;
                $score += substr_count($text, '？') * 10;

                if ($score > 50) {
                    $candidates[] = ['text' => $text, 'score' => $score];
                }
            }
        }

        // Sort by score and return the best candidate
        if (!empty($candidates)) {
            usort($candidates, function($a, $b) {
                return $b['score'] - $a['score'];
            });

            $bestCandidate = $candidates[0]['text'];
            $this->log("Synopsis extracted: " . substr($bestCandidate, 0, 100) . "...");
            return $bestCandidate;
        }

        $this->log("Synopsis extraction failed - no matching content found", 'warning');
        return '';
    }
    
    /**
     * Extract publication date
     */
    private function extractPublishDate(DOMDocument $dom): ?string {
        // Look for update time text
        $xpath = new DOMXPath($dom);
        $textNodes = $xpath->query('//text()[contains(., "更新时间")]');
        
        foreach ($textNodes as $textNode) {
            $text = $textNode->textContent;
            $date = $this->extractDate($text);
            if ($date) {
                $this->log("Publication date extracted: {$date}");
                return $date;
            }
        }

        $this->log("Publication date extraction failed - no matching elements found", 'warning');
        return null;
    }
    
    /**
     * Extract total chapters (estimate from chapter list page)
     */
    private function extractTotalChapters(DOMDocument $dom): int {
        // Count chapter links on the page
        $chapterElements = $this->querySelectorAll($dom, 'a[href*="/read/"]');
        $count = 0;

        foreach ($chapterElements as $element) {
            $href = $element->getAttribute('href');
            $text = $element->textContent;

            if (preg_match('/\/read\/\d+_\d+\.html/', $href) &&
                !$this->isNavigationText($text)) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Validate if a link is a valid chapter link
     */
    private function isValidChapterLink(string $chapterUrl, string $chapterTitle, string $novelId): bool {
        // Must be a read URL with correct novel ID
        if (!preg_match('/\/read\/' . $novelId . '_\d+\.html/', $chapterUrl)) {
            return false;
        }

        // Must have a title
        if (empty($chapterTitle)) {
            return false;
        }

        // Filter out navigation and non-chapter links
        if ($this->isNavigationText($chapterTitle)) {
            return false;
        }

        return true;
    }

    /**
     * Check if text is navigation/non-content text
     */
    private function isNavigationText(string $text): bool {
        $navigationPatterns = [
            '/^(首页|女频|排行榜|完本|客户端|我的书架|上一章|下一章|书页|返回|设置)$/',
            '/^(女生|玄幻|奇幻|武侠|仙侠|都市|言情|军事|历史|科幻|悬疑|完本|APP)$/',
            '/^(简体中文|繁体中文|大熊猫文学|网站地图)$/',
            '/^(语音速度|语音音调|前一段|暂停|继续|停止|下一段|字体|风格|模式|白天|黑夜)$/',
            '/^(点这里听书|已支持|浏览器)$/',
            '/^第\d+--\d+章$/', // Chapter range links like "第0000--0100章"
            '/更新时间/',
            '/最新章节/',
            '/章节目录/',
            '/作者其他作品/',
            '/精彩推荐/'
        ];

        foreach ($navigationPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extract chapter title with fallback selectors
     */
    private function extractChapterTitle(DOMDocument $dom): string {
        $titleSelectors = [
            'h1',
            'h2',
            '.chapter-title',
            '.title'
        ];

        foreach ($titleSelectors as $selector) {
            $titleElement = $this->querySelector($dom, $selector);
            if ($titleElement) {
                $title = $this->cleanText($titleElement->textContent);
                if (!empty($title) && !$this->isNavigationText($title)) {
                    return $title;
                }
            }
        }

        // Fallback: extract from page title
        $titleElements = $this->querySelectorAll($dom, 'title');
        if ($titleElements->length > 0) {
            $pageTitle = $titleElements->item(0)->textContent;
            // Extract chapter title from page title
            if (preg_match('/^([^-]+)/', $pageTitle, $matches)) {
                return $this->cleanText($matches[1]);
            }
        }

        return '';
    }

    /**
     * Find content element with fallback selectors
     */
    private function findContentElement(DOMDocument $dom): ?DOMElement {
        // First try specific selectors for dxmwx
        $contentSelectors = [
            '.content',
            '.chapter-content',
            '.novel-content',
            '#content',
            'main'
        ];

        foreach ($contentSelectors as $selector) {
            $contentElement = $this->querySelector($dom, $selector);
            if ($contentElement) {
                // Check if element has meaningful content
                $text = trim($contentElement->textContent);
                if (!empty($text) && strlen($text) > 100) {
                    return $contentElement;
                }
            }
        }

        // Fallback: look for paragraphs with substantial content
        $xpath = new DOMXPath($dom);

        // Look for elements that contain Chinese text and are likely content
        $contentCandidates = $xpath->query('//p[string-length(normalize-space(.)) > 50] | //div[string-length(normalize-space(.)) > 200]');

        $bestElement = null;
        $bestScore = 0;

        foreach ($contentCandidates as $element) {
            $text = $this->cleanText($element->textContent);

            // Skip navigation elements
            if ($this->isNavigationText($text) ||
                preg_match('/(设置|上一章|下一章|书页|返回|语音|字体|风格|模式)/', $text)) {
                continue;
            }

            // Score based on content characteristics
            $score = 0;
            $score += strlen($text); // Length
            $score += substr_count($text, '。') * 10; // Chinese periods
            $score += substr_count($text, '，') * 5; // Chinese commas
            $score += substr_count($text, '"') * 5; // Quotes

            // Bonus for story-like content
            if (preg_match('/(他|她|我|你|说|道|想|看|听|感觉|觉得|突然|然后|接着|于是|但是|不过|虽然|因为|所以)/', $text)) {
                $score += 50;
            }

            if ($score > $bestScore) {
                $bestScore = $score;
                $bestElement = $element;
            }
        }

        // If we found a good candidate, return its parent to get more context
        if ($bestElement && $bestScore > 100) {
            // Try to get the parent container that might have all the content
            $parent = $bestElement->parentNode;
            while ($parent && $parent->nodeType === XML_ELEMENT_NODE) {
                $parentText = $this->cleanText($parent->textContent);
                if (strlen($parentText) > strlen($this->cleanText($bestElement->textContent)) * 1.5 &&
                    !$this->isNavigationText($parentText)) {
                    $bestElement = $parent;
                    $parent = $parent->parentNode;
                } else {
                    break;
                }
            }
            return $bestElement;
        }

        return null;
    }

    /**
     * Extract chapter text content
     */
    private function extractChapterText(DOMElement $contentElement): string {
        // Get text content while preserving paragraph structure
        $content = $this->getTextContentWithParagraphs($contentElement);

        // Clean up the content
        $content = $this->cleanChapterContent($content);
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        $content = trim($content);

        return $content;
    }

    /**
     * Get text content while preserving paragraph structure
     */
    private function getTextContentWithParagraphs(DOMElement $element): string {
        $result = '';

        foreach ($element->childNodes as $child) {
            if ($child->nodeType === XML_TEXT_NODE) {
                $text = $child->textContent;
                // Skip if it's just whitespace or navigation text
                if (!empty(trim($text)) && !$this->isNavigationText($text)) {
                    $result .= $text;
                }
            } elseif ($child->nodeType === XML_ELEMENT_NODE) {
                switch (strtolower($child->nodeName)) {
                    case 'br':
                        $result .= "\n";
                        break;
                    case 'p':
                    case 'div':
                        $childText = $this->getTextContentWithParagraphs($child);
                        if (!empty(trim($childText)) && !$this->isNavigationText($childText)) {
                            $result .= $childText;
                            if ($child->nodeName === 'p') {
                                $result .= "\n\n";
                            } else {
                                $result .= "\n";
                            }
                        }
                        break;
                    default:
                        $childText = $this->getTextContentWithParagraphs($child);
                        if (!empty(trim($childText)) && !$this->isNavigationText($childText)) {
                            $result .= $childText;
                        }
                        break;
                }
            }
        }

        return $result;
    }

    /**
     * Clean chapter content
     */
    private function cleanChapterContent(string $content): string {
        // Remove HTML entities
        $content = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Remove navigation elements that might have slipped through
        $content = preg_replace('/\s*(设置|上一章|下一章|书页|返回|语音速度|语音音调|前一段|暂停|继续|停止|下一段|字体|风格|模式|白天|黑夜|点这里听书|已支持|浏览器)\s*/', '', $content);

        // Clean up excessive whitespace
        $content = preg_replace('/[ \t]+/', ' ', $content);
        $content = preg_replace('/\n[ \t]*\n/', "\n", $content);

        // Split by paragraphs, clean each, then rejoin
        $paragraphs = preg_split('/\n\s*\n/', $content);
        $cleanedParagraphs = [];

        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            if (!empty($paragraph) && !$this->isNavigationText($paragraph)) {
                $cleanedParagraphs[] = $paragraph;
            }
        }

        return implode("\n\n", $cleanedParagraphs);
    }
}
